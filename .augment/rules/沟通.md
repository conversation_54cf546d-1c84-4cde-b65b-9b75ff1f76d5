---
type: "always_apply"
---

# 协作沟通规则

## 🎯 核心协作原则

### 三大基础原则
- **"Stop and think"** - 复杂任务执行前先暂停思考
- **"Small steps, fast iterations"** - 小步快跑，快速迭代
- **"Documentation first"** - 文档优先

### 基本沟通原则
- 我需要先解释理解和建议方案，等待您确认后再执行
- 质量和稳定性比速度更重要
- 避免上下文丢失、任务细节遗漏和引入新错误

## 🚨 复杂任务执行规则

### 暂停回顾触发条件
- 当任务超过1小时或涉及5个以上文件修改时必须暂停回顾
- 每个步骤不超过30分钟且有明确验证标准

### 编码前必须明确
- 修改文件列表
- 验证检查项
- 风险点识别
- 回滚计划

## 📋 复杂项目实施协作模式

### 阶段管理流程
1. **阶段开始前** - 先说明具体目标和预期结果
2. **实施过程中** - 提供详细的代码修改和验证步骤
3. **阶段完成后** - 提供状态快照和下一步计划
4. **中断处理** - 提供快速恢复指南和当前状态总结

### 实施跟踪文档要求
建立实施跟踪文档，必须包含：
- 核心目标
- 当前问题
- 实施方案
- 检查点
- 回滚计划

### 上下文保持机制
- 使用代码注释标记
- 遵循Git提交规范
- 维护实施日志
- 每个阶段完成后记录状态快照便于中断恢复

## ✅ 执行检查清单

### 任务开始前检查
- [ ] 是否理解了用户需求？
- [ ] 是否制定了详细计划？
- [ ] 是否识别了风险点？
- [ ] 是否准备了回滚方案？

### 执行过程中检查
- [ ] 是否按小步骤执行？
- [ ] 是否及时验证结果？
- [ ] 是否记录了关键变更？
- [ ] 是否保持了上下文？

### 阶段完成后检查
- [ ] 是否达成了预期目标？
- [ ] 是否记录了状态快照？
- [ ] 是否准备了下一步计划？
- [ ] 是否更新了跟踪文档？