# 配置规范检查报告
检查时间: 2025-08-11 00:47:56

## 📊 检查摘要
- 合规分数: 65.0/100
- 违规问题: 2个
- 警告问题: 3个
- 改进建议: 3个

## 🚨 违规问题
### CONFIG_DUPLICATION - HIGH
**文件**: unified_config.yaml
**位置**: message_templates.greeting
**描述**: 发现14个问候模板，存在重复
**建议**: 合并相似的问候模板，保留3-5个核心模板即可

### CONFIG_DUPLICATION - HIGH
**文件**: unified_config.yaml
**位置**: keyword_rules vs keywords_config.yaml
**描述**: 关键词配置在多个地方重复定义
**建议**: 使用统一的keywords_config.yaml，移除unified_config.yaml中的重复配置

## 💡 改进建议
### 统一问候模板管理 - HIGH
**描述**: 将分散的问候模板统一管理，减少重复
**行动**: 保留3-5个核心问候模板，移除重复和相似的模板

### 完成关键词配置迁移 - HIGH
**描述**: 将所有关键词配置迁移到keywords_config.yaml
**行动**: 移除unified_config.yaml中的keyword_rules配置

### 规范化配置命名 - MEDIUM
**描述**: 统一配置键名的命名规范
**行动**: 使用标准的命名约定，如snake_case，避免缩写
