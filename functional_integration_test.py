#!/usr/bin/env python3
"""
功能集成验证脚本
测试修改后的文件是否正常工作
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

import traceback
from backend.config.unified_config_loader import get_unified_config

def test_strategy_imports():
    """测试策略文件导入"""
    print("🔧 开始策略文件导入验证...")
    
    strategy_modules = [
        ("backend.agents.strategies.fallback_strategy", "FallbackStrategy"),
        ("backend.agents.strategies.requirement_strategy", "RequirementStrategy"),
        ("backend.agents.strategies.capabilities_strategy", "CapabilitiesStrategy"),
        ("backend.agents.strategies.emotional_support_strategy", "EmotionalSupportStrategy"),
        ("backend.agents.strategies.knowledge_base_strategy", "KnowledgeBaseStrategy"),
    ]
    
    failed_imports = []
    
    for module_path, class_name in strategy_modules:
        try:
            module = __import__(module_path, fromlist=[class_name])
            strategy_class = getattr(module, class_name)
            print(f"  ✓ {module_path}.{class_name}")
        except Exception as e:
            failed_imports.append((module_path, class_name, str(e)))
            print(f"  ❌ {module_path}.{class_name}: {e}")
    
    if failed_imports:
        print(f"\n⚠️  导入失败的策略:")
        for module, cls, error in failed_imports:
            print(f"    - {module}.{cls}: {error}")
        return False
    else:
        print(f"\n🎉 所有策略文件导入成功!")
        return True

def test_handler_imports():
    """测试处理器文件导入"""
    print("\n🔧 开始处理器文件导入验证...")
    
    handler_modules = [
        ("backend.handlers.conversation_handler", "ConversationHandler"),
        ("backend.handlers.knowledge_base_handler", "KnowledgeBaseHandler"),
        ("backend.handlers.document_handler", "DocumentHandler"),
        ("backend.handlers.composite_handler", "CompositeHandler"),
    ]
    
    failed_imports = []
    
    for module_path, class_name in handler_modules:
        try:
            module = __import__(module_path, fromlist=[class_name])
            handler_class = getattr(module, class_name)
            print(f"  ✓ {module_path}.{class_name}")
        except Exception as e:
            failed_imports.append((module_path, class_name, str(e)))
            print(f"  ❌ {module_path}.{class_name}: {e}")
    
    if failed_imports:
        print(f"\n⚠️  导入失败的处理器:")
        for module, cls, error in failed_imports:
            print(f"    - {module}.{cls}: {error}")
        return False
    else:
        print(f"\n🎉 所有处理器文件导入成功!")
        return True

def test_agent_imports():
    """测试Agent文件导入"""
    print("\n🔧 开始Agent文件导入验证...")
    
    agent_modules = [
        ("backend.agents.base", "AutoGenBaseAgent"),
        ("backend.agents.dynamic_reply_generator", "DynamicReplyGenerator"),
        ("backend.agents.review_and_refine", "ReviewAndRefineAgent"),
        ("backend.agents.rag_knowledge_base_agent", "RAGKnowledgeBaseAgent"),
        ("backend.agents.keyword_accelerator", "KeywordAccelerator"),
        ("backend.agents.conversation_state_machine", "ConversationStateMachine"),
        ("backend.agents.message_reply_manager", "MessageReplyManager"),
    ]
    
    failed_imports = []
    
    for module_path, class_name in agent_modules:
        try:
            module = __import__(module_path, fromlist=[class_name])
            agent_class = getattr(module, class_name)
            print(f"  ✓ {module_path}.{class_name}")
        except Exception as e:
            failed_imports.append((module_path, class_name, str(e)))
            print(f"  ❌ {module_path}.{class_name}: {e}")
    
    if failed_imports:
        print(f"\n⚠️  导入失败的Agent:")
        for module, cls, error in failed_imports:
            print(f"    - {module}.{cls}: {error}")
        return False
    else:
        print(f"\n🎉 所有Agent文件导入成功!")
        return True

def test_message_template_access():
    """测试消息模板访问"""
    print("\n🔧 开始消息模板访问验证...")
    
    try:
        config = get_unified_config()
        
        # 测试不同类型的消息模板访问
        test_cases = [
            # 简单消息模板
            ("message_templates.error.technical_issue", None),
            ("message_templates.greeting.requirement_assistant", None),
            
            # 格式化消息模板
            ("message_templates.base_agent.processing_with_error", {"error_msg": "测试错误"}),
            ("message_templates.keyword_accelerator.default_response", {"intent": "测试"}),
            
            # 多行消息模板
            ("message_templates.error.detailed_clarification_template", {"message_preview": "测试"}),
            ("message_templates.keyword_accelerator.system_capability_query", None),
        ]
        
        failed_access = []
        
        for template_path, format_args in test_cases:
            try:
                template = config.get_config_value(template_path)
                if template:
                    if format_args:
                        result = template.format(**format_args)
                        print(f"  ✓ {template_path}: 格式化访问成功")
                    else:
                        result = template
                        print(f"  ✓ {template_path}: 直接访问成功")
                else:
                    failed_access.append((template_path, "模板为空"))
                    print(f"  ❌ {template_path}: 模板为空")
            except Exception as e:
                failed_access.append((template_path, str(e)))
                print(f"  ❌ {template_path}: {e}")
        
        if failed_access:
            print(f"\n⚠️  访问失败的模板:")
            for template, error in failed_access:
                print(f"    - {template}: {error}")
            return False
        else:
            print(f"\n🎉 所有消息模板访问成功!")
            return True
            
    except Exception as e:
        print(f"❌ 消息模板访问验证失败: {e}")
        traceback.print_exc()
        return False

def test_threshold_access():
    """测试阈值配置访问"""
    print("\n🔧 开始阈值配置访问验证...")
    
    try:
        config = get_unified_config()
        
        threshold_configs = [
            "thresholds.keyword_match_threshold",
            "thresholds.strategy.requirement.keyword_match_multiplier",
            "thresholds.strategy.requirement.max_keyword_score",
        ]
        
        failed_access = []
        
        for threshold_path in threshold_configs:
            try:
                value = config.get_config_value(threshold_path)
                if value is not None:
                    print(f"  ✓ {threshold_path}: {value}")
                else:
                    failed_access.append((threshold_path, "配置为空"))
                    print(f"  ❌ {threshold_path}: 配置为空")
            except Exception as e:
                failed_access.append((threshold_path, str(e)))
                print(f"  ❌ {threshold_path}: {e}")
        
        if failed_access:
            print(f"\n⚠️  访问失败的阈值配置:")
            for config_path, error in failed_access:
                print(f"    - {config_path}: {error}")
            return False
        else:
            print(f"\n🎉 所有阈值配置访问成功!")
            return True
            
    except Exception as e:
        print(f"❌ 阈值配置访问验证失败: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始阶段4：功能集成验证")
    print("=" * 60)
    
    # 执行验证
    strategy_ok = test_strategy_imports()
    handler_ok = test_handler_imports()
    agent_ok = test_agent_imports()
    template_ok = test_message_template_access()
    threshold_ok = test_threshold_access()
    
    print("\n" + "=" * 60)
    if all([strategy_ok, handler_ok, agent_ok, template_ok, threshold_ok]):
        print("🎉 阶段4功能集成验证全部通过!")
        sys.exit(0)
    else:
        print("❌ 阶段4功能集成验证存在问题，需要修复!")
        sys.exit(1)
