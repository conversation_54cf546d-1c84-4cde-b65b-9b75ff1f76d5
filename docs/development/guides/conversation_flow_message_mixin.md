# ConversationFlowMessageMixin 文档

## 简介
`ConversationFlowMessageMixin` 是系统中用于消息获取的重要混入类。它包含 18 个消息获取方法，是系统回复机制的核心组成部分。

## 功能
- 提供多种消息获取方法，支持不同场景下的消息处理。
- 与 `MessageReplyManager` 和 `DynamicReplyGenerator` 等组件协同工作。

## 方法清单
以下是 `ConversationFlowMessageMixin` 提供的主要方法及其描述：
1. `get_user_message()`：获取用户发送的消息。
2. `get_system_message()`：获取系统生成的消息。
3. `get_error_message()`：获取错误提示消息。
4. `get_confirmation_message()`：获取确认操作的消息。
5. `get_fallback_message()`：获取回退机制的消息。
6. `get_greeting_message()`：获取问候消息。
7. `get_clarification_message()`：获取澄清问题的消息。
8. `get_feedback_message()`：获取用户反馈的消息。
9. `get_summary_message()`：获取对话总结的消息。
10. `get_help_message()`：获取帮助信息的消息。
11. `get_notification_message()`：获取通知类型的消息。
12. `get_warning_message()`：获取警告信息的消息。
13. `get_success_message()`：获取成功操作的消息。
14. `get_failure_message()`：获取失败操作的消息。
15. `get_info_message()`：获取一般信息的消息。
16. `get_debug_message()`：获取调试信息的消息。
17. `get_prompt_message()`：获取提示用户输入的消息。
18. `get_custom_message()`：获取自定义类型的消息。

## 与其他组件的关系
- **MessageReplyManager**：`ConversationFlowMessageMixin` 提供的消息获取方法被 `MessageReplyManager` 调用，用于生成用户回复。
- **DynamicReplyGenerator**：通过动态生成消息内容，增强系统的灵活性。

## 示例
```python
from backend.agents.conversation_flow.mixins import ConversationFlowMessageMixin

class CustomAgent(ConversationFlowMessageMixin):
    def handle_message(self, user_input):
        user_message = self.get_user_message()
        return f"处理后的消息: {user_message}"
```

## 注意事项
- 确保所有消息方法返回的内容符合系统的消息格式。
- 在扩展新消息类型时，需要更新此类。
