# ConversationFlowMessageMixin 文档

## 简介
`ConversationFlowMessageMixin` 是系统中用于消息获取的重要混入类。它包含 18 个异步消息获取方法，将原本分散在主类中的消息方法集中管理，提高代码组织性和可维护性。

## 核心特点
- **统一接口**：所有方法都使用 `_get_reply()` 统一接口
- **异步支持**：所有方法都是异步的，支持动态回复生成
- **上下文感知**：支持传递上下文信息进行个性化回复
- **向后兼容**：保持原有方法签名和行为

## 方法清单
以下是 `ConversationFlowMessageMixin` 提供的 18 个消息获取方法，按功能分类：

### 系统状态相关消息
1. `_get_reset_confirmation_message()`：获取重置确认消息
2. `_get_system_error_message()`：获取系统错误消息
3. `_get_modification_error_message()`：获取修改错误消息

### 文档相关消息
4. `_get_document_finalized_message()`：获取文档最终确认消息
5. `_get_document_generated_message()`：获取文档生成完成消息
6. `_get_document_generation_failed_message()`：获取文档生成失败消息
7. `_get_document_generator_not_initialized_message()`：获取文档生成器未初始化消息
8. `_get_document_not_found_message()`：获取文档未找到消息

### 用户交互相关消息
9. `_get_greeting_message()`：获取问候消息
10. `_get_default_requirement_prompt()`：获取默认需求提示消息
11. `_get_initial_guidance_message()`：获取初始引导消息
12. `_get_clarification_request_message()`：获取澄清请求消息
13. `_get_document_refinement_message()`：获取文档细化消息
14. `_get_specific_requirement_help_message()`：获取具体需求帮助消息

### 领域和分类相关消息
15. `_get_domain_category_error_message(domain_result=None, category_result=None, current_domain=None, current_category=None)`：获取领域分类错误消息
16. `_get_domain_info_error_message()`：获取领域信息错误消息
17. `_get_domain_info_fetch_error_message()`：获取领域信息获取错误消息

### 通用错误和处理消息
17. `_get_processing_error_message(error_msg: str)`：获取处理错误消息
18. `_get_unknown_action_message()`：获取未知操作消息

## 依赖关系
`ConversationFlowMessageMixin` 需要与其他组件配合使用：

### 必需依赖
- **ConversationFlowReplyMixin**：提供 `_get_reply()` 方法，是消息获取的核心接口
- 主类需要同时继承这两个混入类才能正常工作

### 组件协作流程
1. `ConversationFlowMessageMixin` 的方法调用 `self._get_reply(reply_key)`
2. `_get_reply()` 方法由 `ConversationFlowReplyMixin` 提供
3. `_get_reply()` 内部调用 `MessageReplyManager.get_reply()` 获取实际回复
4. 如果需要动态生成，会调用 `DynamicReplyGenerator` 生成个性化回复

## 使用方式

### 正确的继承方式
```python
from backend.agents.conversation_flow_message_mixin import ConversationFlowMessageMixin
from backend.agents.conversation_flow_reply_mixin import ConversationFlowReplyMixin
from backend.agents.base import AutoGenBaseAgent

class CustomAgent(AutoGenBaseAgent, ConversationFlowReplyMixin, ConversationFlowMessageMixin):
    """自定义Agent，同时继承回复系统和消息混入"""

    async def handle_greeting(self):
        # 调用消息获取方法
        greeting = await self._get_greeting_message()
        return greeting

    async def handle_error(self, error_context):
        # 带上下文的消息获取
        error_msg = await self._get_processing_error_message(str(error_context))
        return error_msg
```

### 实际项目中的使用
```python
# 在 ConversationFlow 主类中的使用示例
class AutoGenConversationFlowAgent(AutoGenBaseAgent, ConversationFlowReplyMixin, ConversationFlowMessageMixin):

    async def handle_reset_request(self, session_id: str):
        """处理重置请求"""
        # 执行重置逻辑...

        # 获取重置确认消息
        confirmation = await self._get_reset_confirmation_message()
        return confirmation

    async def handle_document_generation_error(self, error_details: str):
        """处理文档生成错误"""
        # 记录错误...

        # 获取错误消息
        error_msg = await self._get_document_generation_failed_message()
        return error_msg
```

## 设计优势
- **代码分离**：将消息方法从主类中分离，减少主类复杂度
- **易于维护**：消息相关的修改只需在此文件中进行
- **可复用**：其他需要类似消息功能的类也可以使用此混入
- **清晰结构**：消息方法集中管理，便于查找和修改

## 注意事项
- 必须与 `ConversationFlowReplyMixin` 一起使用
- 所有方法都是异步的，调用时需要使用 `await`
- 方法名都以 `_get_` 开头，表示这些是内部方法
- 在扩展新消息类型时，需要同时更新此类和相关配置文件
