# 会话状态管理文档

## 简介
会话状态管理是对话系统的核心模块之一，负责跟踪和管理用户会话的状态。通过 `ConversationStateMachine` 和 `UnifiedStateManager`，系统能够高效地处理复杂的状态流转。

## 功能
- 跟踪用户会话的当前状态。
- 管理状态的流转逻辑。
- 提供状态恢复和持久化功能。

## 主要组件
1. **ConversationStateMachine**：实现会话状态的流转逻辑。
2. **UnifiedStateManager**：统一管理所有会话状态。

## 状态流转图
(此处应插入状态流转图，展示状态之间的关系)

## 工作流程
1. 用户输入被传递到 `ConversationStateMachine`。
2. 根据当前状态和输入，决定下一个状态。
3. 使用 `UnifiedStateManager` 持久化状态。
4. 返回状态相关的响应给用户。

## 示例
```python
from backend.agents.state_management import ConversationStateMachine

state_machine = ConversationStateMachine()
current_state = state_machine.get_current_state()
next_state = state_machine.transition("user_input")
print(f"当前状态: {current_state}, 下一个状态: {next_state}")
```

## 注意事项
- 确保状态流转逻辑清晰且无歧义。
- 在扩展新状态时，需要更新状态流转图和相关逻辑。
